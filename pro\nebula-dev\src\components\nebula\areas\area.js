import { FBXLoader } from "three/examples/jsm/loaders/FBXLoader.js";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import {
  TextureLoader,
  AmbientLight,
  Color,
  RepeatWrapping,
  ClampToEdgeWrapping,
} from "three";
import { GUI } from "three/examples/jsm/libs/lil-gui.module.min.js";

/**
 * 单个邻域星云效果
 * @param {THREE.Scene} scene 场景
 */
export default function useTest(scene) {
  const textureLoader = new TextureLoader();
  const loader = new GLTFLoader();

  const ambientLight = new AmbientLight(0xffffff, 8);
  scene.add(ambientLight);
  const gui = new GUI();
  const params = {
    // 关照强度
    light: 8,
    emissive: 0xffffff,
    emissiveIntensity: 1,
    //糙度
    roughness: 0.5,
    // 透明度
    opacity: 1,
  };

  loader.load("/xy2.glb", (group) => {
    function updateMaterial() {
      // ambientLight.intensity = params.light;
      const material = group.scene.children[0].material;
      // // 透明度纹理
      // material.transparent = true;
      // material.alphaMap = material.map;
      // // material.emissive = new Color(params.emissive);
      // material.emissiveIntensity = params.emissiveIntensity;
      // // material.roughness = params.roughness;
      material.opacity = params.opacity;
    }
    updateMaterial();
    gui.add(params, "light", 0, 1000).onChange(updateMaterial);
    gui.addColor(params, "emissive").onChange(updateMaterial);
    gui.add(params, "emissiveIntensity", 0, 1000).onChange(updateMaterial);
    // gui.add(params, "roughness", 0, 20).onChange(updateMaterial);
    gui.add(params, "opacity", 0, 1).onChange(updateMaterial);

    const model = group.scene;
    model.children[0].material.depthWrite = false;

    console.log(model.children[0]);
    model.children[0].scale.x = 0.5;
    model.children[0].scale.y = 0.5;
    model.children[0].scale.z = 0.5;

    function animation() {
      model.children[0].rotation.y += 0.01;
      requestAnimationFrame(animation);
    }
    scene.add(model);
    // animation();
  });
}

/**
 * 生成单个领域模型
 */
export function createArea() {
  
}

/**
 * 计算单层邻域坐标位置
 * @param {object} option 参数
 */
function computeSingleAreaPos(
  option = {
    count: 10, //邻域数量
    y: 0, // 层级y值
    baseRadius: 5, // 基础半径
    radiusStep: 2, // 半径步长
    angleOffsetStep: Math.PI / 10, // 每圈错开18度
  }
) {
  const { count, y: crtY, baseRadius, radiusStep, angleOffsetStep } = option;
  const posArr = [];
  for (let i = 0; i < count; i++) {
    const r = baseRadius + i * radiusStep; // 当前层半径
    const angleOffset = i * angleOffsetStep; // 当前层旋转角度

    for (let j = 0; j < 5; j++) {
      // 五角星跳跃144度
      const angle = angleOffset + j * ((2 * Math.PI * 2) / 5); // = j * 144°
      const x = r * Math.cos(angle);
      const y = r * Math.sin(angle);
      posArr.push([x, crtY, z]);
    }
  }
  return posArr;
}
