import {
  WebGLRenderer,
  Scene,
  PerspectiveCamera,
  TextureLoader,
  EquirectangularReflectionMapping,
  AmbientLight,
} from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";
import envImg from "../nebula/assets/env_c.jpg";
const textureLoader = new TextureLoader();
export default function useTree() {
  const envTexture = textureLoader.load(envImg);
  envTexture.mapping = EquirectangularReflectionMapping;
  const scene = new Scene();
  scene.background = envTexture;
  scene.environmentMap = envTexture;
  scene.backgroundIntensity = 0.05;
  scene.backgroundBlurriness = 0.0;

  const ambientLight = new AmbientLight(0xffffff, 8);
  scene.add(ambientLight);

  // 创建相机
  const camera = new PerspectiveCamera(
    60,
    window.innerWidth / window.innerHeight,
    0.1,
    100
  );
  camera.position.set(1.9, 4, 10);

  // 创建渲染器
  const renderer = new WebGLRenderer();
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.setSize(window.innerWidth, window.innerHeight);

  //   创建轨道控制器
  const controls = new OrbitControls(camera, renderer.domElement);
 

  function resize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
  }
  window.onresize = resize;

  return { renderer, scene, camera };
}
