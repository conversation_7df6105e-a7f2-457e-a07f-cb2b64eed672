<template>
  <div ref="threeContainer"></div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import useThree from "../hooks/useThree";
import useCloud from "./cloud";
import useStars from "./stars";
import useCenter from "./center";
import useTest from "./area";
import generateArea from "./areas";

const threeContainer = ref(null);
const { scene, renderer, camera } = useThree();
// const { cloudMaterial } = useCloud(scene);
// useTest(scene);
// const { update } = useStars(scene);
// useCenter(scene);

function animate() {
  // cloudMaterial.uniforms.cameraPos.value.copy(camera.position);
  // cloudMaterial.uniforms.frame.value++;
  // update();
  renderer.render(scene, camera);
  requestAnimationFrame(animate);
}

async function init() {
  threeContainer.value.appendChild(renderer.domElement);
  const area = await generateArea({
    ele: threeContainer.value,
    camera,
    scene,
  });
  scene.add(area);

  animate();
}
onMounted(init);
</script>

<style scoped></style>
